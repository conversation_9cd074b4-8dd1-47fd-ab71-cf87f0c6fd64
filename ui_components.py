# -*- coding: utf-8 -*-
"""
UI组件模块 - ui_components.py
负责歌谱投影控制器的所有UI组件创建、管理和样式设置
"""

import tkinter as tk
from tkinter import ttk
import sqlite3
import time
from pathlib import Path


class UIComponents:
    """UI组件管理器 - 负责所有UI组件的创建、管理和样式设置"""
    
    def __init__(self, main_app):
        """初始化UI组件管理器
        
        Args:
            main_app: 主应用实例(ImageProjector)
        """
        self.main_app = main_app
        self.root = main_app.root
        
        # UI组件引用
        self.menu_frame = None
        self.project_frame = None
        self.project_tree = None
        self.project_scrollbar = None
        self.search_frame = None
        self.search_scope_frame = None
        self.search_entry = None
        self.search_var = None
        self.search_icon = None
        self.search_scope_label = None
        self.search_scope_combo = None
        self.search_scope_var = None
        self.indicator_frame = None
        self.preview_line = None
        self.next_keyframe_line = None
        
        # 菜单和按钮引用
        self.import_menu = None
        self.font_menu = None
        self.zoom_menu = None
        self.context_menu = None
        self.canvas_menu = None  # 画布右键菜单
        
        # 主菜单按钮引用
        self.btn_import = None
        self.btn_show = None
        self.btn_sync = None
        self.btn_top = None
        self.btn_original = None
        self.btn_zoom = None
        self.btn_invert = None
        self.btn_simple_invert = None
        self.btn_font = None
        self.btn_contact = None
        
        # 关键帧控制按钮
        self.keyframe_frame = None
        self.btn_add_keyframe = None
        self.btn_clear_keyframes = None
        self.btn_prev = None
        self.btn_next = None

        # 倒计时显示组件
        self.countdown_frame = None
        self.countdown_label = None
        self.btn_pause_resume = None
        
        # 样式和字体设置
        self.button_style = {}
        self.button_spacing = 0
        
        # UI组件管理器初始化完成
    
    def create_all_components(self, create_layout=False):
        """创建所有UI组件 - 主入口方法

        Args:
            create_layout: 是否创建主布局（默认False，保持向后兼容）
        """
        try:
            # 按依赖关系顺序创建组件
            self.init_dpi_scaling()
            self.load_font_settings()
            self.init_search_styles()

            if create_layout:
                # 创建主布局框架
                self.create_main_layout()
                # 创建canvas和滚动条
                self.create_canvas_and_scrollbar()
                # 初始化滚动选项
                self.init_scroll_options()
            else:
                # 创建基础框架（这些在main.py中已经创建，这里只是获取引用）
                self._get_existing_frames()

            # 创建具体组件
            self.create_menu_bar()
            self.create_project_tree()
            self.create_keyframe_indicator()
            self.create_preview_lines()
            self.create_canvas_context_menu()  # 创建画布右键菜单

            # 创建联系方式按钮
            self.create_contact_button()

            # 设置窗口事件绑定
            self.setup_window_events()

            # 现在可以安全调用update_screen_list，因为screen_combo已经创建
            if hasattr(self.main_app, 'update_screen_list'):
                self.main_app.update_screen_list()

            # 现在可以安全调用load_projects，因为project_tree已经创建
            if hasattr(self.main_app, 'load_projects'):
                self.main_app.load_projects()

            # 更新播放次数按钮显示
            if hasattr(self.main_app, '_update_play_count_button'):
                self.main_app._update_play_count_button()

            # 初始化滚动引擎（在canvas和projection_manager都创建后）
            if hasattr(self.main_app, 'init_scroll_engine'):
                self.main_app.init_scroll_engine()

        except Exception as e:
            print(f"创建UI组件时出错: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def _get_existing_frames(self):
        """获取main.py中已创建的基础框架引用"""
        # 这些框架在main.py的__init__中已经创建
        if hasattr(self.main_app, 'menu_frame'):
            self.menu_frame = self.main_app.menu_frame
        else:
            print("警告: menu_frame未找到，将在create_menu_bar中创建")
        # 其他框架引用将在后续步骤中添加

    def create_main_layout(self):
        """创建主布局框架 - 从main.py迁移"""
        # 创建顶部菜单栏Frame
        screen_width = self.root.winfo_screenwidth()
        menu_height = int(screen_width / 20)
        self.menu_frame = tk.Frame(self.root, bg="#f0f0f0", height=menu_height)
        self.menu_frame.pack(fill=tk.X, side=tk.TOP)
        self.menu_frame.pack_propagate(False)

        # 保存到主应用中以保持向后兼容性
        self.main_app.menu_frame = self.menu_frame

        # 主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.main_app.main_frame = self.main_frame

        # 创建左右分栏
        self.paned = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.paned.pack(fill=tk.BOTH, expand=True)
        self.main_app.paned = self.paned

        # 左侧项目列表框架 - 添加最小宽度和初始宽度
        self.left_frame = ttk.Frame(self.paned, width=250)  # 设置初始宽度
        self.left_frame.pack_propagate(False)  # 防止子组件影响框架大小
        self.paned.add(self.left_frame)  # 移除 minsize 参数
        self.main_app.left_frame = self.left_frame

        # 右侧主内容框架
        self.right_frame = ttk.Frame(self.paned)
        self.paned.add(self.right_frame, weight=1)
        self.main_app.right_frame = self.right_frame

        # 在窗口显示后设置分隔线位置
        self.root.after(100, self.set_initial_pane_position)

        # 设置左侧面板的初始宽度（在添加完所有组件后设置）
        self.root.update_idletasks()  # 确保组件已经完成初始布局

    def create_canvas_and_scrollbar(self):
        """创建canvas和滚动条 - 从main.py迁移"""
        # 初始化滚动条和画布
        self.scrollbar = tk.Scrollbar(
            self.right_frame,
            orient=tk.VERTICAL,
            width=60,
            command=self.main_app.on_scroll,
            bg="#8B00FF",  # 紫色背景
            activebackground="#9932CC",  # 鼠标悬停时的颜色
            troughcolor="#4B0082",  # 滚动槽的颜色（深紫色）
        )
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.main_app.scrollbar = self.scrollbar

        # 设置画布和滚动条的关联
        self.canvas = tk.Canvas(self.right_frame, bg="black")
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        self.main_app.canvas = self.canvas

        # 绑定鼠标滚轮事件
        self.canvas.bind("<MouseWheel>", self.main_app.on_mousewheel)

        # 初始化高性能滚动引擎（需要在projection_manager初始化后）
        # 这将在主应用的_init_keyframe_state方法中完成

    def init_scroll_options(self):
        """初始化滚动选项 - 从main.py迁移"""
        # 滚动缓动选项（全局） - 在创建画布右键菜单之前初始化
        self.main_app.scroll_easing_options = {
            "线性滚动": "linear",
            "优化三次": "optimized_cubic",  # 新增：性能最佳
            "快速启动": "ease_out_expo",
            "贝塞尔曲线": "bezier",
            "CSS缓入缓出": "css_ease_in_out",  # 新增：CSS ease-in-out
        }
        self.main_app.current_easing = "optimized_cubic"  # 使用优化版本作为默认
        self.main_app.scroll_easing_var = tk.StringVar(value=self.main_app.current_easing)

        # 从配置文件加载用户的选择（如果存在）
        try:
            if self.main_app.config_file.exists():
                import json
                with open(self.main_app.config_file, "r", encoding="utf-8") as f:
                    cfg = json.load(f)
                    if "scroll_easing" in cfg:
                        self.main_app.current_easing = cfg["scroll_easing"]
                        self.main_app.scroll_easing_var.set(self.main_app.current_easing)
        except Exception:
            pass

    def set_initial_pane_position(self):
        """设置初始分隔线位置 - 从main.py迁移"""
        def _set_position():
            try:
                # 从数据库读取保存的宽度
                with sqlite3.connect(self.main_app.db_path) as conn:
                    cursor = conn.execute(
                        "SELECT value FROM ui_settings WHERE key = ?", ("pane_width",)
                    )
                    result = cursor.fetchone()

                    if result:
                        saved_width = int(result[0])
                        self.paned.sashpos(0, saved_width)
                    else:
                        # 使用默认宽度
                        self.paned.sashpos(0, 452)

                self.left_frame.pack_propagate(True)

                # 绑定宽度变化事件
                self.paned.bind("<ButtonRelease-1>", self.on_pane_resize)
                # 绑定拖动事件，实时保存
                self.paned.bind("<B1-Motion>", self.on_pane_resize)

            except Exception as e:
                print(f"设置初始分隔线位置失败: {e}")

        # 使用after方法延迟执行，确保窗口完全加载
        self.root.after(100, _set_position)

    def on_pane_resize(self, event):
        """处理分隔线位置变化事件 - 从main.py迁移"""
        try:
            # 获取当前宽度
            current_width = self.paned.sashpos(0)

            # 保存到数据库
            with sqlite3.connect(self.main_app.db_path) as conn:
                conn.execute(
                    """
                    INSERT OR REPLACE INTO ui_settings (key, value)
                    VALUES (?, ?)
                """,
                    ("pane_width", str(current_width)),
                )
                conn.commit()

        except Exception as e:
            print(f"保存分隔线位置失败: {e}")

    def setup_window_events(self):
        """设置窗口事件绑定 - 从main.py迁移"""
        # 绑定窗口大小改变事件
        self.root.bind("<Configure>", self.on_window_resize)

        # 保留左右方向键绑定用于在原图模式下切换图片（窗口内使用）
        self.root.bind("<Left>", lambda e: self.main_app.handle_arrow_key("prev"))  # 左键
        self.root.bind("<Right>", lambda e: self.main_app.handle_arrow_key("next"))  # 右键

        # 绑定画布右键菜单
        if hasattr(self, 'canvas'):
            self.canvas.bind("<Button-3>", self.show_canvas_context_menu)

    def on_window_resize(self, event):
        """处理窗口大小改变事件 - 从main.py迁移"""
        # 更新图片显示
        self.main_app.update_image()

        # 更新关键帧指示器
        if hasattr(self.main_app, "indicator_frame"):
            self.main_app.update_keyframe_indicators()
    
    
    # ==================== 字体和DPI设置方法 ====================

    def init_dpi_scaling(self):
        """初始化DPI缩放支持"""
        try:
            # 获取系统DPI信息
            import ctypes

            user32 = ctypes.windll.user32
            # 获取主显示器的DPI
            self.main_app.dpi = (
                user32.GetDpiForSystem() if hasattr(user32, "GetDpiForSystem") else 96
            )
            self.main_app.dpi_factor = self.main_app.dpi / 96.0

            # 备用方法：使用tkinter获取缩放因子
            if self.main_app.dpi_factor == 1.0:  # 如果上面方法未能获取正确DPI
                scale_factor = self.root.winfo_fpixels("1i") / 72
                self.main_app.dpi_factor = scale_factor if scale_factor > 1.0 else 1.0

        except Exception as e:
            print(f"DPI检测失败: {e}")
            self.main_app.dpi_factor = 1.0

        # 简化字号设置：菜单栏默认22号
        self.main_app.font_size = 22
        self.main_app.menu_font_size = int(self.main_app.font_size * 0.8)  # 17.6 ≈ 18
        self.main_app.small_font_size = int(self.main_app.font_size * 0.7)  # 15.4 ≈ 15
        self.main_app.large_font_size = int(self.main_app.font_size * 1.2)  # 26.4 ≈ 26

    def load_font_settings(self):
        """从数据库加载字体设置"""
        try:
            with sqlite3.connect(self.main_app.db_path) as conn:
                cursor = conn.execute(
                    "SELECT value FROM ui_settings WHERE key = 'font_size'"
                )
                result = cursor.fetchone()

                if result:
                    self.main_app.font_size = int(result[0])
                    self.main_app.menu_font_size = int(self.main_app.font_size * 0.8)
                    self.main_app.small_font_size = int(self.main_app.font_size * 0.7)
                    self.main_app.large_font_size = int(self.main_app.font_size * 1.2)
                else:
                    # 使用默认设置 - 固定为22号
                    self.main_app.font_size = 22
                    self.main_app.menu_font_size = int(self.main_app.font_size * 0.8)
                    self.main_app.small_font_size = int(self.main_app.font_size * 0.7)
                    self.main_app.large_font_size = int(self.main_app.font_size * 1.2)

                    # 保存默认设置到数据库
                    self.save_font_settings()
        except Exception as e:
            # 使用默认设置 - 固定为22号
            self.main_app.font_size = 22
            self.main_app.menu_font_size = int(self.main_app.font_size * 0.8)
            self.main_app.small_font_size = int(self.main_app.font_size * 0.7)
            self.main_app.large_font_size = int(self.main_app.font_size * 1.2)
            print(f"加载字体设置失败: {e}，使用默认设置: {self.main_app.font_size}")

    def save_font_settings(self):
        """保存字体设置到数据库"""
        try:
            with sqlite3.connect(self.main_app.db_path) as conn:
                # 删除旧的字体设置
                conn.execute("DELETE FROM ui_settings WHERE key = 'font_size'")

                # 插入新的字体设置
                conn.execute(
                    "INSERT INTO ui_settings (key, value) VALUES (?, ?)",
                    ("font_size", str(self.main_app.font_size)),
                )
                conn.commit()
        except Exception as e:
            print(f"保存字体设置失败: {e}")

    def set_font_size(self, size):
        """设置特定字体大小"""
        self.main_app.font_size = size
        self.main_app.menu_font_size = int(self.main_app.font_size * 0.8)
        self.main_app.small_font_size = int(self.main_app.font_size * 0.7)
        self.main_app.large_font_size = int(self.main_app.font_size * 1.2)

        # 更新界面上的所有字体
        self.update_all_fonts()

        # 保存字体设置到数据库
        self.save_font_settings()

    def update_all_fonts(self):
        """更新界面上的所有字体"""
        try:
            # 更新菜单框架中的所有按钮字体
            if self.menu_frame:
                for child in self.menu_frame.winfo_children():
                    try:
                        if isinstance(child, tk.Button):
                            child.configure(font=(self.main_app.default_font, self.main_app.menu_font_size))
                        elif isinstance(
                            child, tk.Frame
                        ):  # 处理嵌套的帧，如关键帧控制按钮组
                            for sub_child in child.winfo_children():
                                try:
                                    if isinstance(sub_child, tk.Button):
                                        sub_child.configure(
                                            font=(self.main_app.default_font, self.main_app.menu_font_size)
                                        )
                                except Exception:
                                    pass
                        elif isinstance(child, ttk.Label):  # 更新标签字体
                            child.configure(font=(self.main_app.default_font, self.main_app.menu_font_size))
                    except Exception:
                        pass

            # 更新搜索相关组件的字体
            self.apply_search_fonts()

            # 更新项目树字体
            self.apply_project_tree_fonts()

            # 更新菜单项字体
            if self.import_menu:
                try:
                    for i in range(self.import_menu.index("end") + 1):
                        self.import_menu.entryconfig(
                            i, font=(self.main_app.default_font, self.main_app.menu_font_size)
                        )
                except Exception:
                    pass

            if self.font_menu:
                try:
                    for i in range(self.font_menu.index("end") + 1):
                        self.font_menu.entryconfig(
                            i, font=(self.main_app.default_font, self.main_app.menu_font_size)
                        )
                except Exception:
                    pass

            if self.zoom_menu:
                try:
                    for i in range(self.zoom_menu.index("end") + 1):
                        self.zoom_menu.entryconfig(
                            i, font=(self.main_app.default_font, self.main_app.menu_font_size)
                        )
                except Exception:
                    pass

            # 更新右键菜单字体
            if self.context_menu:
                try:
                    for i in range(self.context_menu.index("end") + 1):
                        self.context_menu.entryconfig(
                            i, font=(self.main_app.default_font, self.main_app.menu_font_size)
                        )
                except Exception:
                    pass

            # 更新脚本窗口字体（如果存在）
            self._update_script_window_fonts()

            # 强制更新UI
            self.root.update_idletasks()
        except Exception as e:
            print(f"更新字体失败: {e}")

    def _update_script_window_fonts(self):
        """更新脚本窗口的字体（如果窗口存在）"""
        try:
            # 查找脚本窗口
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Toplevel) and widget.title() == "脚本信息编辑":
                    # 找到脚本窗口，更新其字体
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Frame):
                            for sub_child in child.winfo_children():
                                if isinstance(sub_child, tk.Label):
                                    if "关键帧时间脚本" in sub_child.cget("text"):
                                        # 标题
                                        sub_child.configure(
                                            font=(
                                                self.main_app.default_font,
                                                self.main_app.large_font_size,
                                                "bold",
                                            )
                                        )
                                    else:
                                        # 说明文字
                                        sub_child.configure(
                                            font=(
                                                self.main_app.default_font,
                                                self.main_app.menu_font_size,
                                            )
                                        )
                                elif isinstance(sub_child, tk.Button):
                                    # 保存按钮
                                    sub_child.configure(
                                        font=(self.main_app.default_font, self.main_app.menu_font_size)
                                    )
                                elif isinstance(sub_child, tk.Frame):
                                    # 文本框框架
                                    for text_child in sub_child.winfo_children():
                                        if isinstance(text_child, tk.Text):
                                            # 文本框
                                            text_child.configure(
                                                font=(self.main_app.default_font, self.main_app.font_size)
                                            )
                                            break
                    break
        except Exception as e:
            print(f"更新脚本窗口字体失败: {e}")

    def apply_search_fonts(self):
        """应用字体设置到搜索组件"""
        try:
            # 使用样式系统设置ttk组件的字体
            style = ttk.Style()

            # 简化padding计算
            base_padding = 6
            label_padding = 6

            # 配置搜索组件的样式
            style.configure(
                "Search.TEntry",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
                padding=(base_padding, base_padding),
            )
            style.configure(
                "Search.TLabel",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
                padding=(base_padding, label_padding),
            )
            style.configure(
                "Search.TCombobox",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
                padding=(base_padding, base_padding),
            )

            # 配置下拉框的箭头按钮样式
            style.configure(
                "Search.TCombobox.TButton",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
            )

            # 应用样式到搜索组件
            if self.search_entry:
                self.search_entry.configure(style="Search.TEntry")
            if self.search_icon:
                self.search_icon.configure(style="Search.TLabel")
            if self.search_scope_label:
                self.search_scope_label.configure(style="Search.TLabel")
            if self.search_scope_combo:
                self.search_scope_combo.configure(style="Search.TCombobox")

            # 强制刷新下拉框选项字体
            try:
                if self.search_scope_combo and self.search_scope_var:
                    # 重新设置下拉框选项以应用新字体
                    current_value = self.search_scope_var.get()
                    self.update_search_scope_options()
                    if current_value in self.search_scope_combo["values"]:
                        self.search_scope_var.set(current_value)
            except Exception as e:
                print(f"刷新下拉框选项失败: {e}")

            # 强制刷新组件
            self.root.update_idletasks()
        except Exception as e:
            print(f"应用搜索字体失败: {e}")

    def apply_project_tree_fonts(self):
        """应用字体设置到项目树"""
        try:
            style = ttk.Style()
            # 尝试使用支持emoji的字体，按优先级顺序
            font_candidates = [
                "Segoe UI",  # Windows 10/11 默认字体，支持基本emoji
                "Segoe UI Emoji",  # Windows emoji字体
                "Microsoft YaHei UI",  # 原来的字体
                "Arial Unicode MS",  # 备用字体
                "sans-seri",  # 系统默认
            ]

            font_set = False
            for font_name in font_candidates:
                try:
                    # 设置树视图的字体和行高
                    style.configure(
                        "Treeview",
                        font=(font_name, self.main_app.large_font_size),
                        rowheight=int(self.main_app.font_size * 3),
                    )

                    # 设置树视图表头的字体
                    style.configure(
                        "Treeview.Heading", font=(font_name, self.main_app.large_font_size)
                    )

                    font_set = True
                    break
                except Exception:
                    continue

            if not font_set:
                # 如果所有字体都失败，使用默认设置
                style.configure(
                    "Treeview",
                    font=(self.main_app.default_font, self.main_app.large_font_size),
                    rowheight=int(self.main_app.font_size * 3),
                )
                style.configure(
                    "Treeview.Heading", font=(self.main_app.default_font, self.main_app.large_font_size)
                )

            # 强制刷新项目树
            if self.project_tree:
                self.project_tree.update_idletasks()

        except Exception as e:
            print(f"应用项目树字体失败: {e}")

    # ==================== 样式初始化方法 ====================

    def init_search_styles(self):
        """初始化搜索组件的样式"""
        try:
            style = ttk.Style()

            # 简化padding计算
            base_padding = 6
            label_padding = 6

            # 配置搜索组件的样式
            style.configure(
                "Search.TEntry",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
                padding=(base_padding, base_padding),
            )
            style.configure(
                "Search.TLabel",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
                padding=(base_padding, label_padding),
            )
            style.configure(
                "Search.TCombobox",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
                padding=(base_padding, base_padding),
            )

            # 配置下拉框的箭头按钮样式
            style.configure(
                "Search.TCombobox.TButton",
                font=(self.main_app.default_font, self.main_app.menu_font_size),
            )

            # 设置下拉框列表字体
            try:
                self.root.option_add(
                    "*TCombobox*Listbox.font", (self.main_app.default_font, self.main_app.menu_font_size)
                )
                self.root.option_add(
                    "*TEntry*font", (self.main_app.default_font, self.main_app.menu_font_size)
                )
                self.root.option_add(
                    "*TButton*font", (self.main_app.default_font, self.main_app.menu_font_size)
                )
                self.root.option_add(
                    "*TLabel*font", (self.main_app.default_font, self.main_app.menu_font_size)
                )
                self.root.option_add(
                    "*Treeview*font", (self.main_app.default_font, self.main_app.large_font_size)
                )
                self.root.option_add(
                    "*Treeview.Heading*font", (self.main_app.default_font, self.main_app.large_font_size)
                )
            except Exception:
                pass
        except Exception as e:
            print(f"初始化搜索样式失败: {e}")
    
    # ==================== 菜单栏管理方法 ====================

    def create_menu_bar(self):
        """创建主菜单栏和所有按钮"""
        # 确保menu_frame存在
        if not self.menu_frame:
            print("错误: menu_frame未找到，无法创建菜单栏")
            return

        # 进一步减小 padding 值
        padding_x = int(self.main_app.font_size * 0.3)
        padding_y = int(self.main_app.font_size * 0.2)

        # 定义按钮样式
        self.button_style = {
            "relie": "flat",
            "padx": padding_x,
            "pady": padding_y,
            "font": (self.main_app.default_font, self.main_app.menu_font_size),
            "cursor": "hand2",
            "bg": "#f0f0f0",
        }

        self.button_spacing = int(self.main_app.font_size / 6)

        # 创建各种菜单
        self.create_import_menu()
        self.create_zoom_menu()
        self.create_font_menu()

        # 设置下拉框样式
        style = ttk.Style()
        style.configure(
            "TCombobox", padding=5, font=(self.main_app.default_font, self.main_app.menu_font_size)
        )

        # 设置下拉列表样式
        self.root.option_add(
            "*TCombobox*Listbox.font", (self.main_app.default_font, self.main_app.menu_font_size)
        )

        # 创建主菜单按钮
        self.btn_import = tk.Button(
            self.menu_frame, text="导入", command=self.show_import_menu, **self.button_style
        )
        self.btn_show = tk.Button(
            self.menu_frame, text="投影", command=self.main_app.toggle_projection, **self.button_style
        )
        self.btn_sync = tk.Button(
            self.menu_frame, text="同步", command=self.main_app.sync_all_folders, **self.button_style
        )
        self.btn_top = tk.Button(
            self.menu_frame, text="返回", command=self.main_app.reset_view, **self.button_style
        )
        self.btn_original = tk.Button(
            self.menu_frame,
            text="原图",
            command=self.main_app.toggle_original_mode,
            **self.button_style,
        )
        self.btn_zoom = tk.Button(
            self.menu_frame, text="缩放", command=self.show_zoom_menu, **self.button_style
        )
        self.btn_invert = tk.Button(
            self.menu_frame, text="变色", command=self.main_app.toggle_invert, **self.button_style
        )
        self.btn_simple_invert = tk.Button(
            self.menu_frame,
            text="反色",
            command=self.main_app.toggle_simple_invert,
            **self.button_style,
        )
        self.btn_font = tk.Button(
            self.menu_frame, text="字号", command=self.show_font_menu, **self.button_style
        )

        # 布局按钮
        self.btn_import.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_show.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_sync.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_top.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_original.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_zoom.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_invert.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_simple_invert.pack(side=tk.LEFT, padx=self.button_spacing)

        # 添加屏幕选择组件（委托给projection_manager处理）
        self._create_screen_selection()

        # 绑定悬停效果
        for btn in (
            self.btn_import,
            self.btn_show,
            self.btn_sync,
            self.btn_top,
            self.btn_original,
            self.btn_zoom,
            self.btn_invert,
            self.btn_simple_invert,
        ):
            btn.bind("<Enter>", lambda e, b=btn: self.on_hover(b, True))
            btn.bind("<Leave>", lambda e, b=btn: self.on_hover(b, False))

        # 添加关键帧控制按钮组
        self.create_keyframe_controls()

        # 添加字体按钮
        self.btn_font.pack(side=tk.LEFT, padx=self.button_spacing)
        self.btn_font.bind("<Enter>", lambda e, b=self.btn_font: self.on_hover(b, True))
        self.btn_font.bind("<Leave>", lambda e, b=self.btn_font: self.on_hover(b, False))

    def _create_screen_selection(self):
        """创建屏幕选择组件"""
        # 添加屏幕选择下拉框
        ttk.Label(
            self.menu_frame,
            text="屏幕:",
            font=("Microsoft YaHei UI", int(self.main_app.font_size * 0.6)),
        ).pack(side=tk.LEFT, padx=5)

        self.main_app.screen_var = tk.StringVar()
        self.main_app.screen_combo = ttk.Combobox(
            self.menu_frame,
            textvariable=self.main_app.screen_var,
            width=3,
            font=("Microsoft YaHei UI", int(self.main_app.font_size * 0.6)),
            state="readonly",
        )
        self.main_app.screen_combo.pack(side=tk.LEFT, padx=2)

        # 设置下拉框样式
        style = ttk.Style()
        style.configure(
            "TCombobox",
            padding=5,
            font=("Microsoft YaHei UI", int(self.main_app.font_size * 0.6)),
        )

        # 设置下拉列表样式
        self.root.option_add(
            "*TCombobox*Listbox.font", ("Microsoft YaHei UI", int(self.main_app.font_size * 0.6))
        )
        self.root.option_add("*TCombobox*Listbox.selectBackground", "#0078D7")
        self.root.option_add("*TCombobox*Listbox.selectForeground", "white")

    def create_import_menu(self):
        """创建导入菜单"""
        self.import_menu = tk.Menu(self.root, tearoff=0)
        self.import_menu.add_command(
            label="导入单个图片",
            command=self.main_app.import_single_image,
            font=(self.main_app.default_font, self.main_app.menu_font_size),
        )
        self.import_menu.add_command(
            label="导入文件夹",
            command=self.main_app.import_folder,
            font=(self.main_app.default_font, self.main_app.menu_font_size),
        )
        # 添加分隔线
        self.import_menu.add_separator()
        # 添加另存图选项（支持变色和反色）
        self.import_menu.add_command(
            label="另存图",
            command=self.main_app.save_effect_image,
            font=(self.main_app.default_font, self.main_app.menu_font_size),
        )

    def create_font_menu(self):
        """创建字体菜单"""
        self.font_menu = tk.Menu(self.root, tearoff=0)
        font_sizes = [18, 20, 22, 30, 35, 40]
        for size in font_sizes:
            self.font_menu.add_command(
                label=f"{size}号",
                command=lambda s=size: self.set_font_size(s),
                font=(self.main_app.default_font, self.main_app.menu_font_size),
            )

    def create_zoom_menu(self):
        """创建缩放菜单"""
        self.zoom_menu = tk.Menu(self.root, tearoff=0)
        self.zoom_menu.add_command(
            label="还原",
            command=self.main_app.zoom_reset_wrapper,
            font=(self.main_app.default_font, self.main_app.menu_font_size),
        )
        self.zoom_menu.add_command(
            label="放大",
            command=self.main_app.zoom_in_wrapper,
            font=(self.main_app.default_font, self.main_app.menu_font_size),
        )
        self.zoom_menu.add_command(
            label="缩小",
            command=self.main_app.zoom_out_wrapper,
            font=(self.main_app.default_font, self.main_app.menu_font_size),
        )

    def show_import_menu(self):
        """显示导入菜单"""
        x = self.btn_import.winfo_rootx()
        y = self.btn_import.winfo_rooty() + self.btn_import.winfo_height()
        self.import_menu.post(x, y)

    def show_font_menu(self):
        """显示字体菜单"""
        x = self.btn_font.winfo_rootx()
        y = self.btn_font.winfo_rooty() + self.btn_font.winfo_height()
        self.font_menu.post(x, y)

    def show_zoom_menu(self):
        """显示缩放菜单"""
        x = self.btn_zoom.winfo_rootx()
        y = self.btn_zoom.winfo_rooty() + self.btn_zoom.winfo_height()
        self.zoom_menu.post(x, y)

    def on_hover(self, button, entering):
        """处理按钮悬停效果"""
        # 如果是投影显示按钮且投影窗口存在，保持绿色
        if button == self.btn_show and hasattr(self.main_app, 'second_window') and self.main_app.second_window:
            button.configure(bg="#90EE90")
            return

        # 如果是反色按钮且反色已启用，保持绿色
        if button == self.btn_invert and hasattr(self.main_app, 'is_inverted') and self.main_app.is_inverted:
            button.configure(bg="#90EE90")
            return

        # 如果是普通反色按钮且普通反色已启用，保持绿色
        if button == self.btn_simple_invert and hasattr(self.main_app, 'is_simple_inverted') and self.main_app.is_simple_inverted:
            button.configure(bg="#90EE90")
            return

        # 如果是原图模式按钮且原图模式已启用，保持绿色
        if button == self.btn_original and hasattr(self.main_app, 'original_mode') and self.main_app.original_mode:
            button.configure(bg="#90EE90")
            return

        # 如果是录制按钮，检查录制状态
        if hasattr(self.main_app, 'btn_record') and button == self.main_app.btn_record:
            if hasattr(self.main_app, 'is_recording_timing') and self.main_app.is_recording_timing:
                # 正在录制时的悬停效果：深红 ↔ 浅红
                button.configure(bg="#ff6666" if entering else "#ffcccc")
            else:
                # 未录制时的正常悬停效果，但保持初始的浅红背景
                button.configure(bg="#e5e5e5" if entering else "#ffcccc")
            return

        # 如果是播放按钮，检查播放状态
        if hasattr(self.main_app, 'btn_play') and button == self.main_app.btn_play:
            if hasattr(self.main_app, 'is_auto_playing') and self.main_app.is_auto_playing:
                # 正在播放时保持绿色，悬停时稍微深一点
                button.configure(bg="#7FDD7F" if entering else "#90EE90")
            else:
                # 未播放时的正常悬停效果
                button.configure(bg="#e5e5e5" if entering else "#f0f0f0")
            return

        # 如果是暂停/继续按钮，根据状态使用不同颜色
        if hasattr(self.main_app, 'btn_pause_resume') and button == self.main_app.btn_pause_resume:
            # 检查当前是否在暂停状态
            is_paused = (hasattr(self.main_app, 'auto_player') and
                        hasattr(self.main_app.auto_player, 'is_paused') and
                        self.main_app.auto_player.is_paused)

            if is_paused:
                # 暂停状态：绿色
                button.configure(bg="#90EE90" if entering else "#98FB98")
            else:
                # 正常状态：普通颜色
                button.configure(bg="#e5e5e5" if entering else "#f0f0f0")
            return

        # 其他情况正常悬停效果
        button.configure(bg="#e5e5e5" if entering else "#f0f0f0")

    def create_keyframe_controls(self):
        """创建关键帧控制按钮组"""
        # 创建一个Frame来容纳关键帧控制按钮
        self.keyframe_frame = tk.Frame(self.menu_frame, bg="#f0f0f0")
        self.keyframe_frame.pack(side=tk.LEFT, padx=10)

        # 设置按钮样式 - 与主菜单按钮保持一致
        keyframe_button_style = {
            "relie": "flat",
            "padx": int(self.main_app.font_size * 0.3),
            "pady": int(self.main_app.font_size * 0.2),
            "font": (self.main_app.default_font, self.main_app.menu_font_size),
            "cursor": "hand2",
        }

        self.btn_add_keyframe = tk.Button(
            self.keyframe_frame,
            text="加帧",
            command=self.main_app.add_current_keyframe,
            **keyframe_button_style,
        )

        # 清除关键帧按钮
        self.btn_clear_keyframes = tk.Button(
            self.keyframe_frame,
            text="清帧",
            command=self.main_app.clear_current_keyframes,
            **keyframe_button_style,
        )

        # 上一个关键帧按钮
        self.btn_prev = tk.Button(
            self.keyframe_frame,
            text="上帧",
            command=self.main_app.handle_pageup_key,
            **keyframe_button_style,
        )

        # 下一个关键帧按钮
        self.btn_next = tk.Button(
            self.keyframe_frame,
            text="下帧",
            command=self.main_app.handle_pagedown_key,
            **keyframe_button_style,
        )

        # 播放次数按钮
        self.main_app.btn_loop = tk.Button(
            self.keyframe_frame, text="1次", command=self.main_app.set_play_count, **keyframe_button_style
        )

        # 时间录制按钮
        self.main_app.btn_record = tk.Button(
            self.keyframe_frame,
            text="录制",
            command=self.main_app.toggle_timing_recording,
            bg="#ffcccc",
            **keyframe_button_style,
        )

        # 自动播放按钮
        self.main_app.btn_play = tk.Button(
            self.keyframe_frame,
            text="播放",
            command=self.main_app.toggle_auto_play,
            **keyframe_button_style,
        )

        # 清除时间按钮
        self.main_app.btn_clear_timing = tk.Button(
            self.keyframe_frame,
            text="清时",
            command=self.main_app.clear_timing_data,
            **keyframe_button_style,
        )

        # 脚本信息按钮
        self.main_app.btn_script_info = tk.Button(
            self.keyframe_frame,
            text="脚本",
            command=self.main_app.show_script_info,
            **keyframe_button_style,
        )

        # 打包按钮 - 调整间距和顺序
        self.btn_add_keyframe.pack(side=tk.LEFT, padx=3)
        self.btn_clear_keyframes.pack(side=tk.LEFT, padx=3)

        # 添加分隔间距
        separator1 = tk.Frame(self.keyframe_frame, width=8, bg="#f0f0f0")
        separator1.pack(side=tk.LEFT)

        self.btn_prev.pack(side=tk.LEFT, padx=3)
        self.btn_next.pack(side=tk.LEFT, padx=3)

        # 添加分隔间距
        separator2 = tk.Frame(self.keyframe_frame, width=8, bg="#f0f0f0")
        separator2.pack(side=tk.LEFT)

        # 播放、次数、录制按钮顺序
        self.main_app.btn_play.pack(side=tk.LEFT, padx=3)
        self.main_app.btn_loop.pack(side=tk.LEFT, padx=3)
        self.main_app.btn_record.pack(side=tk.LEFT, padx=3)

        # 添加分隔间距
        separator3 = tk.Frame(self.keyframe_frame, width=8, bg="#f0f0f0")
        separator3.pack(side=tk.LEFT)

        self.main_app.btn_script_info.pack(side=tk.LEFT, padx=3)
        self.main_app.btn_clear_timing.pack(side=tk.LEFT, padx=3)

        # 将暂停按钮引用保存到主应用中
        self.main_app.btn_pause_resume = self.btn_pause_resume

        # 为所有关键帧控制按钮添加悬停效果
        keyframe_buttons = [
            self.btn_add_keyframe,      # 加帧
            self.btn_clear_keyframes,   # 清帧
            self.btn_prev,              # 上帧
            self.btn_next,              # 下帧
            self.main_app.btn_play,     # 播放
            self.main_app.btn_loop,     # 播放次数
            self.main_app.btn_record,   # 录制
            self.main_app.btn_script_info,  # 脚本
            self.main_app.btn_clear_timing, # 清时
        ]

        for btn in keyframe_buttons:
            if btn:  # 确保按钮存在
                btn.bind("<Enter>", lambda e, b=btn: self.on_hover(b, True))
                btn.bind("<Leave>", lambda e, b=btn: self.on_hover(b, False))

    def create_contact_button(self):
        """创建联系方式按钮"""
        self.btn_contact = tk.Button(
            self.menu_frame,
            text="作者",
            command=self.main_app.show_contact_info,
            **self.button_style
        )
        self.btn_contact.pack(side=tk.LEFT, padx=self.button_spacing)

        # 绑定悬停效果
        self.btn_contact.bind("<Enter>", lambda e: self.on_hover(self.btn_contact, True))
        self.btn_contact.bind("<Leave>", lambda e: self.on_hover(self.btn_contact, False))

        # 将按钮引用保存到主应用中以保持向后兼容性
        self.main_app.btn_contact = self.btn_contact

        # 在作者按钮后面添加倒计时显示
        self.create_countdown_display()

    def create_countdown_display(self):
        """创建倒计时显示组件"""
        # 减少分隔间距，让倒计时更靠近作者按钮
        separator_countdown = tk.Frame(self.menu_frame, width=5, bg="#f0f0f0")
        separator_countdown.pack(side=tk.LEFT)

        # 创建暂停按钮（放在作者后面），使用与播放按钮相同的样式
        self.btn_pause_resume = tk.Button(
            self.menu_frame,
            text="暂停",
            command=self.main_app.toggle_countdown_pause,
            bg="#f0f0f0",  # 默认普通颜色
            relief="flat",
            padx=8,
            pady=4,
            font=(self.main_app.default_font, self.main_app.menu_font_size),
            cursor="hand2"
        )
        self.btn_pause_resume.pack(side=tk.LEFT, padx=3)

        # 添加小间距
        separator_data = tk.Frame(self.menu_frame, width=3, bg="#f0f0f0")
        separator_data.pack(side=tk.LEFT)

        # 创建倒计时显示框架
        self.countdown_frame = tk.Frame(self.menu_frame, bg="#f0f0f0")
        self.countdown_frame.pack(side=tk.LEFT, padx=2)

        # 只显示"倒"参数标签（橙色高亮，字体更大但标签框适中）
        self.countdown_dao_label = tk.Label(
            self.countdown_frame,
            text="倒: --",
            font=(self.main_app.default_font, self.main_app.menu_font_size + 4, "bold"),  # 字体大4号
            bg="#ffffcc",  # 浅黄色背景高亮
            fg="#cc6600",  # 深橙色文字
            relief="sunken",
            bd=1,  # 边框恢复1像素
            width=10,  # 宽度适中
            height=1  # 高度恢复1行
        )
        self.countdown_dao_label.pack(side=tk.LEFT, padx=2)

        # 将暂停按钮引用保存到主应用中
        self.main_app.btn_pause_resume = self.btn_pause_resume

        # 为暂停按钮添加悬停效果
        self.btn_pause_resume.bind("<Enter>", lambda e: self.on_hover(self.btn_pause_resume, True))
        self.btn_pause_resume.bind("<Leave>", lambda e: self.on_hover(self.btn_pause_resume, False))

        # 将组件引用保存到主应用中
        self.main_app.countdown_dao_label = self.countdown_dao_label
        # 保持向后兼容性
        self.main_app.countdown_label = self.countdown_dao_label

        # 倒计时组件固定显示，不隐藏

    # ==================== 项目树和搜索组件方法 ====================

    def create_project_tree(self):
        """创建项目列表树组件"""
        # 创建左侧面板的框架
        self.project_frame = ttk.Frame(self.main_app.left_frame)
        self.project_frame.pack(fill=tk.BOTH, expand=True)

        # 创建搜索面板
        self.create_search_panel()

        # 创建项目树
        self.project_tree = ttk.Treeview(self.project_frame, selectmode="browse")
        self.project_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加滚动条
        self.project_scrollbar = ttk.Scrollbar(
            self.project_frame, orient="vertical", command=self.project_tree.yview
        )
        self.project_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.project_tree.configure(yscrollcommand=self.project_scrollbar.set)

        # 设置列
        self.project_tree["columns"] = "name"
        self.project_tree.column("#0", width=0, stretch=tk.NO)  # 隐藏图标列
        self.project_tree.column("name", width=250, anchor=tk.W)  # 增加名称列宽度

        # 设置表头
        self.project_tree.heading("#0", text="")
        self.project_tree.heading("name", text="项目")

        # 立即应用支持emoji的字体样式
        self.apply_project_tree_fonts()

        # 绑定选择事件
        self.project_tree.bind("<<TreeviewSelect>>", self.main_app.on_project_select)

        # 绑定右键菜单
        self.project_tree.bind("<Button-3>", self.main_app.show_context_menu)

        # 创建右键菜单（完全动态构建）
        self.context_menu = tk.Menu(self.root, tearoff=0)
        # 所有菜单项将在show_context_menu中根据项目类型动态添加

        # 初始化拖放数据
        self.main_app.drag_data = {"item": None, "x": 0, "y": 0, "target": None}

        # 绑定拖放事件
        self.project_tree.bind("<ButtonPress-1>", self.main_app.on_tree_press)
        self.project_tree.bind("<B1-Motion>", self.main_app.on_tree_motion)
        self.project_tree.bind("<ButtonRelease-1>", self.main_app.on_tree_release)

    def create_search_panel(self):
        """创建搜索面板"""
        # 添加搜索框
        self.search_frame = ttk.Frame(self.project_frame)
        self.search_frame.pack(fill=tk.X, padx=5, pady=5)

        # 添加搜索图标
        self.search_icon = ttk.Label(
            self.search_frame, text="🔍", style="Search.TLabel"
        )
        self.search_icon.pack(side=tk.LEFT, padx=(0, 5))

        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(
            self.search_frame, textvariable=self.search_var, style="Search.TEntry"
        )
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 添加搜索范围选择
        self.search_scope_frame = ttk.Frame(self.project_frame)
        self.search_scope_frame.pack(fill=tk.X, padx=5, pady=2)

        self.search_scope_var = tk.StringVar(value="全部")
        self.search_scope_label = ttk.Label(
            self.search_scope_frame, text="搜索范围:", style="Search.TLabel"
        )
        self.search_scope_label.pack(side=tk.LEFT)

        self.search_scope_combo = ttk.Combobox(
            self.search_scope_frame,
            textvariable=self.search_scope_var,
            state="readonly",
            width=15,
            style="Search.TCombobox",
        )
        self.search_scope_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 强制设置下拉框字体
        try:
            self.search_scope_combo.configure(
                font=(self.main_app.default_font, self.main_app.menu_font_size)
            )
        except Exception:
            pass

        # 立即应用字体设置到搜索组件
        self.apply_search_fonts()

        # 初始化搜索范围下拉框
        self.update_search_scope_options()

        # 设置搜索相关事件绑定
        self.setup_search_events()

    def setup_search_events(self):
        """设置搜索相关事件绑定"""
        # 绑定回车键搜索
        self.search_entry.bind("<Return>", lambda e: self.main_app.search_projects())

        # 绑定双击事件，自动清空搜索框内容
        self.search_entry.bind("<Double-Button-1>", self.clear_search_on_double_click)

        # 绑定搜索框内容变化事件，实现实时搜索
        self.search_var.trace_add(
            "write", lambda name, index, mode: self.main_app.search_projects()
        )

    def clear_search_on_double_click(self, event):
        """双击搜索框时自动清空内容"""
        try:
            # 清空搜索框内容
            self.search_var.set("")
            # 将光标移到开头
            self.search_entry.icursor(0)
            # 触发搜索以重新加载所有项目
            self.main_app.search_projects()
        except Exception as e:
            print(f"清空搜索框失败: {e}")

    def update_search_scope_options(self):
        """更新搜索范围选项"""
        try:
            options = ["全部"]

            # 从数据库获取所有文件夹
            with sqlite3.connect(self.main_app.db_path) as conn:
                cursor = conn.execute(
                    "SELECT id, name FROM folders ORDER BY order_index"
                )
                folders = cursor.fetchall()

                for folder_id, folder_name in folders:
                    options.append(f"{folder_name} (ID:{folder_id})")

            # 更新下拉框选项
            self.search_scope_combo["values"] = options

            # 如果当前选择不在选项中，重置为"全部"
            if self.search_scope_var.get() not in options:
                self.search_scope_var.set("全部")

        except Exception as e:
            print(f"更新搜索范围选项失败: {e}")
    
    # ==================== 关键帧组件方法 ====================

    def create_keyframe_indicator(self):
        """创建关键帧指示器"""
        # 创建指示器框架
        self.indicator_frame = tk.Canvas(
            self.main_app.right_frame, width=20, bg="#000000", highlightthickness=0
        )
        self.indicator_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定点击事件
        self.indicator_frame.bind("<Button-1>", self.on_indicator_click)

    def update_keyframe_indicators(self):
        """更新关键帧指示器显示"""
        if not self.indicator_frame:
            return

        # 清除现有标记
        self.indicator_frame.delete("all")
        if hasattr(self.main_app, 'keyframe_markers'):
            self.main_app.keyframe_markers.clear()

        if not hasattr(self.main_app, "current_image_id"):
            return

        # 获取当前图片的关键帧
        keyframes = self.main_app.get_keyframes(self.main_app.current_image_id)
        if not keyframes:
            return

        # 获取指示器高度
        height = self.indicator_frame.winfo_height()

        # 绘制关键帧标记
        for i, (kf_id, position, order_index) in enumerate(keyframes):
            y = position * height

            # 当前帧使用绿色，其他帧使用红色
            if i == self.main_app.current_keyframe_index:
                color = "#00FF00"  # 当前帧为绿色
                width = 20  # 当前帧使用更粗的线条
            else:
                color = "#FF0000"  # 其他帧为红色
                width = 17  # 其他帧使用稍细的线条

            # 绘制横线标记
            marker = self.indicator_frame.create_line(
                2, y, 18, y, fill=color, width=width, tags=f"marker_{kf_id}"
            )

            # 在线条两端添加圆点装饰
            self.indicator_frame.create_oval(
                1,
                y - 2,
                5,
                y + 2,  # 左侧圆点
                fill=color,
                outline=color,
                tags=f"marker_{kf_id}",
            )
            self.indicator_frame.create_oval(
                15,
                y - 2,
                19,
                y + 2,  # 右侧圆点
                fill=color,
                outline=color,
                tags=f"marker_{kf_id}",
            )

            # 存储标记信息
            if not hasattr(self.main_app, 'keyframe_markers'):
                self.main_app.keyframe_markers = []
            self.main_app.keyframe_markers.append(
                {"id": kf_id, "position": position, "marker": marker, "y": y}
            )

    def on_indicator_click(self, event):
        """处理指示器点击事件"""
        if not hasattr(self.main_app, 'keyframe_markers') or not self.main_app.keyframe_markers:
            return

        # 特殊处理：如果当前有滚动动画正在进行，立即停止
        if hasattr(self.main_app, "scroll_engine") and self.main_app.scroll_engine.is_running():
            self.main_app.scroll_engine.stop_animation()
            print("检测到滚动动画正在进行，点击关键帧指示器立即停止")

        clicked_y = event.y
        closest_marker = min(
            self.main_app.keyframe_markers, key=lambda m: abs(m["y"] - clicked_y)
        )

        # 查找点击的关键帧索引
        keyframes = self.main_app.get_keyframes(self.main_app.current_image_id)
        for i, (kf_id, _, _) in enumerate(keyframes):
            if kf_id == closest_marker["id"]:
                self.main_app.current_keyframe_index = i
                break

        # 点击关键帧指示器总是直接跳转
        self.main_app.canvas.yview_moveto(closest_marker["position"])

        # 同步投影
        if self.main_app.second_window and self.main_app.sync_enabled:
            self.main_app.sync_projection_screen_absolute()

        # 更新预览线
        self.update_preview_lines()

        print(f"直接跳转到关键帧 {self.main_app.current_keyframe_index + 1}")

        # 更新指示器
        self.update_keyframe_indicators()

    # ==================== 画布右键菜单组件方法 ====================

    def create_canvas_context_menu(self):
        """创建画布右键菜单"""
        self.canvas_menu = tk.Menu(
            self.root, tearoff=0, font=(self.main_app.default_font, int(self.main_app.font_size * 0.8))
        )

        # 添加滚动速度子菜单
        self.scroll_speed_menu = tk.Menu(
            self.canvas_menu,
            tearoff=0,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )
        self.main_app.scroll_speed_var = tk.StringVar()
        self.main_app.scroll_speed_var.set(str(self.main_app.scroll_duration))
        for speed in [
            "0",
            "0.5",
            "1.0",
            "8.0",
            "9.0",
            "10.0",
            "11.0",
            "12.0",
            "13.0",
            "14.0",
            "15.0",
        ]:
            self.scroll_speed_menu.add_radiobutton(
                label=f"{speed}秒",
                variable=self.main_app.scroll_speed_var,
                value=speed,
                command=self.main_app.set_scroll_speed,
                font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
            )
        self.canvas_menu.add_cascade(
            label="滚动速度",
            menu=self.scroll_speed_menu,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )

        # 滚动函数子菜单（紧跟在滚动速度后面）
        self.scroll_easing_menu = tk.Menu(
            self.canvas_menu, tearoff=0, font=(self.main_app.default_font, int(self.main_app.font_size * 0.8))
        )
        for name, key in self.main_app.scroll_easing_options.items():
            self.scroll_easing_menu.add_radiobutton(
                label=name,
                variable=self.main_app.scroll_easing_var,
                value=key,
                command=lambda k=key: self.main_app.set_scroll_easing(k),
                font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
            )
        self.canvas_menu.add_cascade(
            label="滚动函数",
            menu=self.scroll_easing_menu,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )

        # 添加黄字颜色子菜单
        self.yellow_color_menu = tk.Menu(
            self.canvas_menu,
            tearoff=0,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )
        # 初始化黄字颜色变量（如果还没有的话）
        if not hasattr(self.main_app, 'yellow_color_var'):
            self.main_app.yellow_color_var = tk.StringVar()
        # 设置当前选中的颜色名称（仅设置名称，不包含RGB，因为radiobutton的value是名称）
        if self.main_app.current_yellow_color_name:
            self.main_app.yellow_color_var.set(self.main_app.current_yellow_color_name)
        self.update_yellow_color_menu()  # 用新的方法动态刷新
        self.canvas_menu.add_cascade(
            label="黄字颜色",
            menu=self.yellow_color_menu,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )

        # 添加原图模式子菜单
        self.original_display_menu = tk.Menu(
            self.canvas_menu,
            tearoff=0,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )
        self.main_app.original_display_var = tk.StringVar()
        # 根据当前状态设置默认值（只在原图模式启用时）
        if self.main_app.original_mode:
            self.main_app.original_display_var.set(self.main_app.original_display_mode)
        else:
            self.main_app.original_display_var.set("stretch")  # 默认拉伸模式

        self.original_display_menu.add_radiobutton(
            label="适中",
            variable=self.main_app.original_display_var,
            value="fit",
            command=lambda: self.main_app.set_original_display_mode("fit"),
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )
        self.original_display_menu.add_radiobutton(
            label="拉伸",
            variable=self.main_app.original_display_var,
            value="stretch",
            command=lambda: self.main_app.set_original_display_mode("stretch"),
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )
        self.canvas_menu.add_cascade(
            label="原图",
            menu=self.original_display_menu,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )

    def show_canvas_context_menu(self, event):
        """显示画布右键菜单"""
        # 确保有图片加载时才显示菜单
        if self.main_app.image:
            # 更新原图显示模式菜单状态
            self.main_app.original_display_var.set(self.main_app.original_display_mode)
            self.canvas_menu.post(event.x_root, event.y_root)

    def update_yellow_color_menu(self):
        """动态刷新黄字颜色菜单，包括自定义颜色和自定义入口，显示RGB参数"""
        self.yellow_color_menu.delete(0, "end")
        for name, rgb in self.main_app.yellow_text_presets.items():
            # 格式化显示：颜色名称 (R,G,B)
            display_label = f"{name} ({rgb['r']},{rgb['g']},{rgb['b']})"
            self.yellow_color_menu.add_radiobutton(
                label=display_label,
                variable=self.main_app.yellow_color_var,
                value=name,
                command=lambda n=name: self.main_app.set_yellow_text_color(n),
                font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
            )
        self.yellow_color_menu.add_separator()
        self.yellow_color_menu.add_command(
            label="颜色选择器...",
            command=self.main_app.add_custom_yellow_color,
            font=(self.main_app.default_font, int(self.main_app.font_size * 0.8)),
        )
    
    # ==================== 预览线组件方法 ====================

    def create_preview_lines(self):
        """创建预览线组件"""
        # 检查canvas是否存在
        if not hasattr(self.main_app, 'canvas') or self.main_app.canvas is None:
            print("警告: canvas未找到，跳过预览线创建")
            self.preview_line = None
            self.next_keyframe_line = None
            return

        # 当前关键帧预览线 - 使用实线
        self.preview_line = self.main_app.canvas.create_line(
            0, 0, 0, 0, fill="#00FF00", width=3, dash=()  # 初始坐标  #  # 实线
        )

        # 下一个关键帧预览线 - 使用虚线
        self.next_keyframe_line = self.main_app.canvas.create_line(
            0,
            0,
            0,
            0,  # 初始坐标
            fill="red",
            width=7,  # 稍细的线条宽度
            dash=(5, 5),  # 虚线样式
        )

        # 初始化更新时间戳
        self.main_app._last_preview_update = 0

    def update_preview_lines(self):
        """更新预览线位置（优化版本：使用缓存的关键帧数据）"""
        if not hasattr(self.main_app, "current_image_id") or not self.preview_line:
            return

        # 添加节流控制（减少延迟）
        current_time = time.time()
        if (
            hasattr(self.main_app, "_last_update_time")
            and current_time - self.main_app._last_update_time < 0.03
        ):
            return
        self.main_app._last_update_time = current_time

        try:
            # 使用缓存的关键帧数据，避免重复数据库查询
            keyframes_data = self.main_app.get_keyframes(self.main_app.current_image_id)
            if not keyframes_data:
                # 如果没有关键帧，隐藏预览线
                self.main_app.canvas.coords(self.preview_line, 0, 0, 0, 0)
                self.main_app.canvas.coords(self.next_keyframe_line, 0, 0, 0, 0)
                return

            # 需要获取y_position，所以还是需要查询数据库，但可以优化查询
            with sqlite3.connect(self.main_app.db_path) as conn:
                # 获取所有关键帧的y_position用于绘制预览线,按order_index排序
                cursor = conn.execute(
                    """
                    SELECT id, y_position, order_index
                    FROM keyframes
                    WHERE image_id = ?
                    ORDER BY order_index ASC
                """,
                    (self.main_app.current_image_id,),
                )
                keyframes = cursor.fetchall()

                if not keyframes:
                    # 如果没有关键帧，隐藏预览线
                    self.main_app.canvas.coords(self.preview_line, 0, 0, 0, 0)
                    self.main_app.canvas.coords(self.next_keyframe_line, 0, 0, 0, 0)
                    return

                # 获取当前画布位置
                current_y = self.main_app.canvas.canvasy(0) + (self.main_app.canvas.winfo_height() / 2)

                # 使用current_keyframe_index来确定当前和下一个关键帧
                if self.main_app.current_keyframe_index >= 0:
                    current_frame = keyframes[self.main_app.current_keyframe_index]
                    next_frame = keyframes[
                        (self.main_app.current_keyframe_index + 1) % len(keyframes)
                    ]
                else:
                    # 找到最接近的关键帧 - 使用y_position进行距离计算
                    current_index = 0
                    min_distance = float("inf")
                    for i, frame in enumerate(keyframes):
                        distance = abs(frame[1] - current_y)  # frame[1]是y_position
                        if distance < min_distance:
                            min_distance = distance
                            current_index = i
                    current_frame = keyframes[current_index]
                    next_frame = keyframes[(current_index + 1) % len(keyframes)]

                # 更新主窗口预览线
                canvas_width = self.main_app.canvas.winfo_width()
                if current_frame:
                    y = current_frame[1]  # 使用y_position绘制预览线
                    self.main_app.canvas.coords(self.preview_line, 0, y, canvas_width, y)
                    self.main_app.canvas.tag_raise(self.preview_line)

                if next_frame:
                    y = next_frame[1]  # 使用y_position绘制预览线
                    self.main_app.canvas.coords(self.next_keyframe_line, 0, y, canvas_width, y)
                    self.main_app.canvas.tag_raise(self.next_keyframe_line)

        except Exception as e:
            print(f"更新预览线失败: {e}")
    
    # ==================== 公共接口方法 ====================
    
    def get_selected_project(self):
        """获取当前选中的项目"""
        if self.project_tree:
            selection = self.project_tree.selection()
            return selection[0] if selection else None
        return None
    
    def refresh_project_tree(self):
        """刷新项目树显示"""
        # 委托给main_app的方法
        if hasattr(self.main_app, 'load_projects'):
            self.main_app.load_projects()
    
    def get_search_term(self):
        """获取当前搜索词"""
        if self.search_var:
            return self.search_var.get()
        return ""
    
    def clear_search(self):
        """清空搜索框"""
        if self.search_var:
            self.search_var.set("")
    